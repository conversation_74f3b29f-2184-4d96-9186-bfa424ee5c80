spring:
  application:
    name: gedsys2-bpm-engine
  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    driver-class-name: org.postgresql.Driver
  web:
    resources:
      add-mappings: false
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USER:guest}
    password: ${RABBITMQ_PASSWORD:guest}
    virtual-host: /
    listener:
      simple:
        acknowledge-mode: manual
        default-requeue-rejected: true

camunda:
  bpm:
    admin-user:
      id: admin
      password: ${CAMUNDA_ADMIN_PASSWORD:admin}
      email: <EMAIL>
      first-name: Admin
      last-name: Admin

server:
  port: 8080

logging:
  level:
    root: error                      # Nivel general restrictivo
    '[org.springframework]': warn        # Warnings importantes de Spring
    '[org.hibernate]': error             # Solo errores de Hibernate
    '[org.apache]': warn                 # Componentes Apache (incluyendo Tomcat)
    '[co.com.gedsys]': info              # Tu código de aplicación (ajusta al paquete real)
  file:
    name: ${LOG_FILE_PATH:/var/log/gedsys/application.log}   # Ruta al archivo de log
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  logback:
    rollingpolicy:
      max-file-size: 10MB            # Tamaño máximo por archivo
      max-history: 30                # Número de archivos históricos a mantener
      total-size-cap: 3GB            # Tamaño máximo de todos los archivos       