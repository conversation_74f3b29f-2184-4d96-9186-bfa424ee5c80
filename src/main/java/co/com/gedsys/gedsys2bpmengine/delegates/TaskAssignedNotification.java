package co.com.gedsys.gedsys2bpmengine.delegates;

import java.time.LocalDateTime;

import org.cibseven.bpm.engine.delegate.DelegateTask;
import org.cibseven.bpm.engine.delegate.Expression;
import org.cibseven.bpm.engine.delegate.TaskListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import co.com.gedsys.commons.constant.amqp.RoutingKeyName;
import co.com.gedsys.commons.constant.amqp.ExchangeName;
import co.com.gedsys.commons.events.task.TaskAssignedEvent;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("taskAssigned")
public class TaskAssignedNotification implements TaskListener {

    private final RabbitTemplate rabbitTemplate;

    private Expression taskTitle;
    private Expression taskFormKey;

    public TaskAssignedNotification(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        var taskTitleValue = (String) taskTitle.getValue(delegateTask);
        var taskFormKeyValue = (String) taskFormKey.getValue(delegateTask);

        log.debug("Task delegate: {} - injected task title: {} - injected task form key: {}", delegateTask,
                taskTitleValue, taskFormKeyValue);

        var taskAssigned = new TaskAssignedEvent(
                delegateTask.getId(),
                taskFormKeyValue,
                delegateTask.getAssignee(),
                delegateTask.getOwner(),
                taskTitleValue,
                delegateTask.getProcessInstanceId(),
                LocalDateTime.now());

        rabbitTemplate.convertAndSend(ExchangeName.MAIN_TOPIC_EXCHANGE, RoutingKeyName.TAREA_ASIGNADA, taskAssigned);
        log.debug("Tarea asignada notificada {}", taskAssigned);
    }

    public void setTaskTitle(Expression taskTitle) {
        this.taskTitle = taskTitle;
    }

    public void setTaskFormKey(Expression taskFormKey) {
        this.taskFormKey = taskFormKey;
    }
}