package co.com.gedsys.gedsys2bpmengine.infrastructure.amqp;

import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.events.process.ProcesoCompletadoParcialmente;
import co.com.gedsys.commons.interfaces.AbstractRabbitMQListener;
import co.com.gedsys.gedsys2bpmengine.constant.TaskVariableName;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import org.cibseven.bpm.engine.RuntimeService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import com.rabbitmq.client.Channel;

@Slf4j
@Component
@RabbitListener(queues = QueueName.PROCESOS_COMPLETADOS_PARCIALMENTE, containerFactory = "manualListenerContainerFactory")
public class ProcessPartiallyCompletedListener extends AbstractRabbitMQListener<ProcesoCompletadoParcialmente> {

    private final RuntimeService runtimeService;

    public ProcessPartiallyCompletedListener(RuntimeService runtimeService, RabbitTemplate rabbitTemplate) {
        super(rabbitTemplate);
        this.runtimeService = runtimeService;
    }

    @RabbitHandler
    public void processMessage(ProcesoCompletadoParcialmente payload,
            Message message,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        super.processMessage(payload, message, channel, deliveryTag);
    }

    protected void handleMessageProcessing(ProcesoCompletadoParcialmente message) {
        log.debug("Procesando mensaje del orquestador: {}", message);

        if (message.correlationMessage() == null || message.processInstanceId() == null) {
            throw new IllegalArgumentException("El mensaje no contiene correlationMessage o processInstanceId");
        }

        Map<String, Object> orchestratorMessageMap = Map.of(
                "correlationMessage", message.correlationMessage(),
                "executionId", message.executionId(),
                "processInstanceId", message.processInstanceId(),
                "processName", message.processName(),
                "publishedAt", message.publishedAt(),
                "resumeUrl", message.resumeUrl(),
                "stakeholders", message.stakeholders(),
                "variables", message.variables(),
                "workflowExecutionId", message.workflowExecutionId());

        log.debug("Saving orchestrator message map: {} as variable: {}", orchestratorMessageMap,
                TaskVariableName.ORCHESTRATOR_MESSAGE);

        runtimeService.createMessageCorrelation(message.correlationMessage())
                .processInstanceId(message.processInstanceId())
                .setVariableLocal(TaskVariableName.ORCHESTRATOR_MESSAGE, orchestratorMessageMap)
                .correlateWithResult();

        log.info("Mensaje correlacionado exitosamente para el proceso: {}", message.processInstanceId());
    }
}